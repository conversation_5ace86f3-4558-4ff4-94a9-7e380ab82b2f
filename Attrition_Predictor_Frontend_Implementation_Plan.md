# 🔮 Attrition Predictor Frontend Implementation Plan

## 📋 Document Overview

This document provides a comprehensive implementation plan for the Attrition Predictor feature in the HRMS system. The feature will be accessible through an expandable AI Features section in the sidebar navigation.

---

## 🎯 UI Navigation Structure

### **Sidebar Navigation Enhancement**
The current sidebar has a single "AI Features" button. We need to modify it to be expandable:

**Current Structure:**
```
├── Dashboard
├── Employees (Admin only)
├── Attendance
├── Leave
├── Payroll
├── Performance
├── Smart Reports (Admin/Manager)
├── AI Chatbot (Employee only)
└── AI Features (All roles) ← MODIFY THIS
```

**New Structure:**
```
└── AI Features (All roles) ← EXPANDABLE
    ├── 🤖 AI Chatbot (Employee only)
    ├── 📊 Smart Reports (Admin/Manager)
    ├── 🔮 Attrition Predictor (Admin only) ← NEW
    ├── 📄 Resume Parser (Admin/Manager)
    └── 🔍 Anomaly Detection (Admin/Manager)
```

### **Navigation Implementation Details**
- **Expand Icon**: Use `ChevronDown` / `ChevronUp` from Lucide React
- **Expand Behavior**: Click to toggle sub-menu visibility
- **Active State**: Highlight both parent and active child
- **Role-Based Access**: Show only relevant sub-items based on user role
- **Mobile Responsive**: Collapsible behavior maintained on mobile

---

## 🎨 Frontend UI Specifications

### **Page Layout: `/admin/attrition-predictor`**

#### **1. Header Section**
```jsx
<div className="mb-6">
  <h1 className="text-3xl font-bold text-foreground">Attrition Predictor</h1>
  <p className="text-muted-foreground mt-2">
    AI-powered employee attrition risk analysis and predictions
  </p>
</div>
```

#### **2. Summary Cards Row**
```jsx
<div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
  <Card className="p-6 bg-gradient-to-br from-red-50 to-red-100 border-red-200">
    <div className="flex items-center justify-between">
      <div>
        <p className="text-red-600 text-sm font-medium">Critical Risk</p>
        <p className="text-3xl font-bold text-red-700">{criticalCount}</p>
      </div>
      <AlertTriangle className="h-8 w-8 text-red-500" />
    </div>
  </Card>
  
  <Card className="p-6 bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200">
    <div className="flex items-center justify-between">
      <div>
        <p className="text-orange-600 text-sm font-medium">High Risk</p>
        <p className="text-3xl font-bold text-orange-700">{highCount}</p>
      </div>
      <TrendingUp className="h-8 w-8 text-orange-500" />
    </div>
  </Card>
  
  <Card className="p-6 bg-gradient-to-br from-yellow-50 to-yellow-100 border-yellow-200">
    <div className="flex items-center justify-between">
      <div>
        <p className="text-yellow-600 text-sm font-medium">Medium Risk</p>
        <p className="text-3xl font-bold text-yellow-700">{mediumCount}</p>
      </div>
      <Users className="h-8 w-8 text-yellow-500" />
    </div>
  </Card>
  
  <Card className="p-6 bg-gradient-to-br from-green-50 to-green-100 border-green-200">
    <div className="flex items-center justify-between">
      <div>
        <p className="text-green-600 text-sm font-medium">Low Risk</p>
        <p className="text-3xl font-bold text-green-700">{lowCount}</p>
      </div>
      <Shield className="h-8 w-8 text-green-500" />
    </div>
  </Card>
</div>
```

#### **3. Controls Section**
```jsx
<div className="flex flex-col sm:flex-row gap-4 mb-6">
  <div className="flex-1">
    <Select value={riskThreshold} onValueChange={setRiskThreshold}>
      <SelectTrigger>
        <SelectValue placeholder="Risk Threshold" />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="0.3">Low Risk (0.3+)</SelectItem>
        <SelectItem value="0.5">Medium Risk (0.5+)</SelectItem>
        <SelectItem value="0.7">High Risk (0.7+)</SelectItem>
        <SelectItem value="0.9">Critical Risk (0.9+)</SelectItem>
      </SelectContent>
    </Select>
  </div>
  
  <div className="flex-1">
    <Select value={departmentFilter} onValueChange={setDepartmentFilter}>
      <SelectTrigger>
        <SelectValue placeholder="Filter by Department" />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="all">All Departments</SelectItem>
        {departments.map(dept => (
          <SelectItem key={dept.id} value={dept.id}>{dept.name}</SelectItem>
        ))}
      </SelectContent>
    </Select>
  </div>
  
  <Button onClick={generatePredictions} disabled={loading}>
    {loading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : <Brain className="h-4 w-4 mr-2" />}
    Generate Predictions
  </Button>
  
  <Button variant="outline" onClick={exportReport}>
    <Download className="h-4 w-4 mr-2" />
    Export Report
  </Button>
</div>
```

#### **4. Predictions Table**
```jsx
<Card className="mb-8">
  <CardHeader>
    <CardTitle className="flex items-center gap-2">
      <Users className="h-5 w-5" />
      Employee Attrition Risk Analysis
    </CardTitle>
  </CardHeader>
  <CardContent>
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Employee</TableHead>
          <TableHead>Department</TableHead>
          <TableHead>Risk Score</TableHead>
          <TableHead>Risk Level</TableHead>
          <TableHead>Key Factors</TableHead>
          <TableHead>Actions</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {predictions.map((prediction) => (
          <TableRow key={prediction.employeeId}>
            <TableCell>
              <div className="flex items-center gap-3">
                <Avatar className="h-8 w-8">
                  <AvatarFallback>{prediction.employeeName.charAt(0)}</AvatarFallback>
                </Avatar>
                <div>
                  <p className="font-medium">{prediction.employeeName}</p>
                  <p className="text-sm text-muted-foreground">ID: {prediction.employeeId}</p>
                </div>
              </div>
            </TableCell>
            <TableCell>{prediction.department}</TableCell>
            <TableCell>
              <div className="flex items-center gap-2">
                <Progress value={prediction.riskScore * 100} className="w-16" />
                <span className="text-sm font-medium">{(prediction.riskScore * 100).toFixed(1)}%</span>
              </div>
            </TableCell>
            <TableCell>
              <Badge variant={getRiskVariant(prediction.riskLevel)}>
                {prediction.riskLevel.toUpperCase()}
              </Badge>
            </TableCell>
            <TableCell>
              <div className="flex flex-wrap gap-1">
                {prediction.factors.slice(0, 2).map((factor, index) => (
                  <Badge key={index} variant="outline" className="text-xs">
                    {factor.replace('_', ' ')}
                  </Badge>
                ))}
                {prediction.factors.length > 2 && (
                  <Badge variant="outline" className="text-xs">
                    +{prediction.factors.length - 2} more
                  </Badge>
                )}
              </div>
            </TableCell>
            <TableCell>
              <div className="flex gap-2">
                <Button size="sm" variant="outline" onClick={() => viewDetails(prediction)}>
                  <Eye className="h-4 w-4" />
                </Button>
                <Button size="sm" variant="outline" onClick={() => generateReport(prediction)}>
                  <FileText className="h-4 w-4" />
                </Button>
              </div>
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  </CardContent>
</Card>
```

#### **5. Employee Detail Modal**
```jsx
<Dialog open={showDetailModal} onOpenChange={setShowDetailModal}>
  <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
    <DialogHeader>
      <DialogTitle className="flex items-center gap-2">
        <User className="h-5 w-5" />
        Attrition Risk Analysis: {selectedEmployee?.name}
      </DialogTitle>
    </DialogHeader>
    
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      {/* Risk Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Risk Overview</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span>Risk Score</span>
              <div className="flex items-center gap-2">
                <Progress value={selectedEmployee?.riskScore * 100} className="w-24" />
                <span className="font-bold">{(selectedEmployee?.riskScore * 100).toFixed(1)}%</span>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <span>Risk Level</span>
              <Badge variant={getRiskVariant(selectedEmployee?.riskLevel)}>
                {selectedEmployee?.riskLevel?.toUpperCase()}
              </Badge>
            </div>
            <div className="flex items-center justify-between">
              <span>Prediction Date</span>
              <span>{formatDate(selectedEmployee?.predictionDate)}</span>
            </div>
          </div>
        </CardContent>
      </Card>
      
      {/* Contributing Factors */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Contributing Factors</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {selectedEmployee?.factors.map((factor, index) => (
              <div key={index} className="flex items-center justify-between p-2 bg-muted rounded">
                <span className="text-sm">{formatFactor(factor)}</span>
                <Badge variant="outline" className="text-xs">
                  {getFactorImpact(factor)}
                </Badge>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
      
      {/* Recommendations */}
      <Card className="md:col-span-2">
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <Lightbulb className="h-5 w-5" />
            AI Recommendations
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {selectedEmployee?.recommendations.map((rec, index) => (
              <div key={index} className="p-4 border rounded-lg">
                <h4 className="font-medium mb-2">{formatRecommendation(rec)}</h4>
                <p className="text-sm text-muted-foreground mb-3">
                  {getRecommendationDescription(rec)}
                </p>
                <Button size="sm" variant="outline">
                  Take Action
                </Button>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  </DialogContent>
</Dialog>
```

---

## 🔐 Role-Based Access Control

### **Admin Role (Full Access)**
- **Permissions**: Complete access to all attrition predictor functionality
- **Features Available**:
  - View all employee attrition predictions
  - Generate predictions for any employee
  - Access detailed risk analysis
  - Export comprehensive reports
  - Modify risk thresholds
  - View historical prediction data
  - Access AI recommendations and action items

### **Manager Role (Limited Access)**
- **Permissions**: Access restricted to team members only
- **Features Available**:
  - View attrition predictions for direct reports only
  - Generate predictions for team members
  - Access basic risk analysis for team
  - Export team-specific reports
  - View recommendations for team members
- **Restrictions**:
  - Cannot view organization-wide data
  - Cannot access other managers' team data
  - Limited historical data access

### **Employee Role (No Access)**
- **Permissions**: No access to attrition predictor
- **Reason**: Sensitive HR data that could impact employee morale
- **Alternative**: Employees can access general AI features like chatbot

---

## 🔧 Technical Implementation Details

### **Component Structure**
```
src/
├── components/
│   └── attrition/
│       ├── AttritionDashboard.jsx          # Main dashboard component
│       ├── AttritionSummaryCards.jsx       # Risk summary cards
│       ├── AttritionTable.jsx              # Predictions table
│       ├── AttritionDetailModal.jsx        # Employee detail modal
│       ├── AttritionFilters.jsx            # Filter controls
│       └── AttritionExport.jsx             # Export functionality
├── pages/
│   └── attrition/
│       └── AttritionPredictorPage.jsx      # Main page wrapper
├── hooks/
│   ├── useAttritionPredictions.js          # Data fetching hook
│   └── useAttritionFilters.js              # Filter state management
└── services/
    └── attritionService.js                 # API service layer
```

### **State Management**
```javascript
// Using React Query for server state
const {
  data: predictions,
  isLoading,
  error,
  refetch
} = useQuery({
  queryKey: ['attrition-predictions', filters],
  queryFn: () => attritionService.getPredictions(filters),
  staleTime: 5 * 60 * 1000, // 5 minutes
  cacheTime: 10 * 60 * 1000 // 10 minutes
});

// Local state for UI interactions
const [selectedEmployee, setSelectedEmployee] = useState(null);
const [showDetailModal, setShowDetailModal] = useState(false);
const [filters, setFilters] = useState({
  riskThreshold: 0.7,
  department: 'all',
  sortBy: 'riskScore',
  sortOrder: 'desc'
});
```

### **API Integration Points**

#### **1. Get Attrition Predictions**
```javascript
// Endpoint: GET /api/ai/attrition-predictions
// Query Parameters: ?riskThreshold=0.7&departmentId=2

const getPredictions = async (filters = {}) => {
  const params = new URLSearchParams();
  if (filters.riskThreshold) params.append('riskThreshold', filters.riskThreshold);
  if (filters.departmentId && filters.departmentId !== 'all') {
    params.append('departmentId', filters.departmentId);
  }
  
  const response = await axiosInstance.get(
    `${API_ENDPOINTS.AI.ATTRITION_PREDICTIONS}?${params.toString()}`
  );
  return response.data;
};
```

#### **2. Generate Individual Prediction**
```javascript
// Endpoint: POST /api/ai/attrition-predictions
// Request Body: { employeeId: 123 }

const generatePrediction = async (employeeId) => {
  const response = await axiosInstance.post(
    API_ENDPOINTS.AI.ATTRITION_PREDICTIONS,
    { employeeId }
  );
  return response.data;
};
```

### **Data Flow Diagram**
```
User Action → Component → Hook → Service → API → Backend → AI Service → Database
     ↓           ↓        ↓       ↓       ↓        ↓          ↓           ↓
  Click Filter → Update → Query → HTTP → Process → Generate → Store → Return
     ↑           ↑        ↑       ↑       ↑        ↑          ↑           ↑
Update UI ← Component ← Hook ← Service ← API ← Backend ← AI Service ← Database
```

### **Error Handling**
```javascript
// Service layer error handling
const handleApiError = (error) => {
  if (error.response?.status === 403) {
    toast.error('Access denied. Admin privileges required.');
    navigate('/dashboard');
  } else if (error.response?.status === 404) {
    toast.error('Employee not found.');
  } else if (error.response?.status >= 500) {
    toast.error('Server error. Please try again later.');
  } else {
    toast.error(error.response?.data?.message || 'An error occurred.');
  }
};

// Component error boundaries
<ErrorBoundary fallback={<AttritionErrorFallback />}>
  <AttritionDashboard />
</ErrorBoundary>
```

### **Loading States**
```javascript
// Skeleton loading for table
{isLoading ? (
  <div className="space-y-4">
    {[...Array(5)].map((_, i) => (
      <div key={i} className="flex items-center space-x-4 p-4">
        <Skeleton className="h-10 w-10 rounded-full" />
        <div className="space-y-2 flex-1">
          <Skeleton className="h-4 w-[200px]" />
          <Skeleton className="h-4 w-[150px]" />
        </div>
        <Skeleton className="h-8 w-[100px]" />
        <Skeleton className="h-8 w-[80px]" />
      </div>
    ))}
  </div>
) : (
  <AttritionTable predictions={predictions} />
)}
```

---

## 📊 Data Visualization Requirements

### **Risk Score Visualization**
- **Progress Bars**: Show risk scores as percentage bars with color coding
- **Color Scheme**:
  - 🟢 Green (0-30%): Low Risk
  - 🟡 Yellow (31-60%): Medium Risk  
  - 🟠 Orange (61-80%): High Risk
  - 🔴 Red (81-100%): Critical Risk

### **Charts and Graphs**
- **Risk Distribution Chart**: Pie chart showing distribution across risk levels
- **Department Risk Comparison**: Bar chart comparing average risk by department
- **Trend Analysis**: Line chart showing risk trends over time
- **Factor Impact Analysis**: Horizontal bar chart showing factor contributions

### **Interactive Elements**
- **Hover Effects**: Show detailed tooltips on chart elements
- **Click Actions**: Navigate to detailed views from chart elements
- **Zoom/Pan**: Allow interaction with time-series charts
- **Export Options**: Save charts as PNG/PDF

---

## 🎨 UI/UX Enhancements

### **Smooth Animations**
```css
/* Smooth transitions for all interactive elements */
.transition-smooth {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Card hover effects */
.card-hover:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* Button hover animations */
.button-hover:hover {
  transform: scale(1.02);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
```

### **Gradient Backgrounds**
```css
/* Subtle gradient backgrounds for cards */
.gradient-red {
  background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
}

.gradient-orange {
  background: linear-gradient(135deg, #fff7ed 0%, #fed7aa 100%);
}

.gradient-yellow {
  background: linear-gradient(135deg, #fefce8 0%, #fde047 100%);
}

.gradient-green {
  background: linear-gradient(135deg, #f0fdf4 0%, #bbf7d0 100%);
}
```

### **Three.js Integration**
```javascript
// 3D visualization for risk analysis
import * as THREE from 'three';
import { Canvas, useFrame } from '@react-three/fiber';

const RiskVisualization3D = ({ predictions }) => {
  return (
    <Canvas className="h-64 w-full">
      <ambientLight intensity={0.5} />
      <pointLight position={[10, 10, 10]} />
      <RiskSpheres predictions={predictions} />
    </Canvas>
  );
};

const RiskSpheres = ({ predictions }) => {
  const meshRef = useRef();
  
  useFrame((state, delta) => {
    meshRef.current.rotation.y += delta * 0.5;
  });
  
  return (
    <group ref={meshRef}>
      {predictions.map((pred, index) => (
        <mesh
          key={pred.employeeId}
          position={[
            (index % 5) * 2 - 4,
            Math.floor(index / 5) * 2 - 2,
            0
          ]}
        >
          <sphereGeometry args={[pred.riskScore, 32, 32]} />
          <meshStandardMaterial 
            color={getRiskColor(pred.riskLevel)}
            transparent
            opacity={0.8}
          />
        </mesh>
      ))}
    </group>
  );
};
```

---

## ✅ Implementation Checklist

### **Phase 1: Navigation Enhancement**
- [ ] Modify Sidebar.jsx to support expandable AI Features
- [ ] Add ChevronDown/Up icons for expand/collapse
- [ ] Implement sub-menu state management
- [ ] Add role-based filtering for sub-items
- [ ] Test responsive behavior on mobile

### **Phase 2: Core Components**
- [ ] Create AttritionPredictorPage.jsx
- [ ] Implement AttritionDashboard.jsx
- [ ] Build AttritionSummaryCards.jsx
- [ ] Develop AttritionTable.jsx
- [ ] Create AttritionDetailModal.jsx

### **Phase 3: Data Integration**
- [ ] Implement attritionService.js
- [ ] Create useAttritionPredictions hook
- [ ] Add error handling and loading states
- [ ] Implement data caching with React Query
- [ ] Add real-time updates

### **Phase 4: Advanced Features**
- [ ] Add export functionality
- [ ] Implement advanced filtering
- [ ] Create data visualizations
- [ ] Add Three.js 3D components
- [ ] Implement smooth animations

### **Phase 5: Testing & Polish**
- [ ] Unit tests for components
- [ ] Integration tests for API calls
- [ ] Accessibility testing
- [ ] Performance optimization
- [ ] Cross-browser testing

---

## 🚀 Next Steps

1. **Review and Approval**: Please review this implementation plan
2. **API Verification**: Confirm all backend APIs are functional
3. **Design Approval**: Approve UI mockups and user flow
4. **Development Start**: Begin implementation in phases
5. **Testing**: Comprehensive testing at each phase
6. **Deployment**: Staged rollout with user feedback

---

**Document Status**: ✅ Ready for Review  
**Last Updated**: 2025-06-15  
**Version**: 1.0  
**Author**: Augment Agent
